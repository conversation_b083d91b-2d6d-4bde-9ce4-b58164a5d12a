﻿I'm sorry, but an uncaught exception occurred.

Compiling ATL code at game/screens.rpy:106
  File "game/script.rpy", line 25, in script
    igor "总是使用同一套卡牌，"
  File "game/screens.rpy", line 95, in execute
    screen say(who, what):
  File "game/screens.rpy", line 95, in execute
    screen say(who, what):
  File "game/screens.rpy", line 97, in execute
    window:
  File "game/screens.rpy", line 101, in execute
    if who is not None:
  File "game/screens.rpy", line 102, in execute
    window:
  File "game/screens.rpy", line 106, in <module>
    zorder -1  # 将姓名框放到较低层级
NameError: name 'zorder' is not defined

-- Full Traceback ------------------------------------------------------------

Full traceback:
  File "game/script.rpy", line 25, in script
    igor "总是使用同一套卡牌，"
  File "D:\femc kaifa\renpy-8.3.7-sdk\renpy\ast.py", line 2586, in execute
    Say.execute(self)
  File "D:\femc kaifa\renpy-8.3.7-sdk\renpy\ast.py", line 623, in execute
    renpy.exports.say(who, what, *args, **kwargs)
  File "D:\femc kaifa\renpy-8.3.7-sdk\renpy\exports\sayexports.py", line 132, in say
    who(what, *args, **kwargs)
  File "D:\femc kaifa\renpy-8.3.7-sdk\renpy\character.py", line 1471, in __call__
    self.do_display(who, what, cb_args=self.cb_args, dtt=dtt, **display_args)
  File "D:\femc kaifa\renpy-8.3.7-sdk\renpy\character.py", line 1117, in do_display
    display_say(who,
  File "D:\femc kaifa\renpy-8.3.7-sdk\renpy\character.py", line 814, in display_say
    rv = renpy.ui.interact(mouse='say', type=type, roll_forward=roll_forward)
  File "D:\femc kaifa\renpy-8.3.7-sdk\renpy\ui.py", line 301, in interact
    rv = renpy.game.interface.interact(roll_forward=roll_forward, **kwargs)
  File "D:\femc kaifa\renpy-8.3.7-sdk\renpy\display\core.py", line 2218, in interact
    repeat, rv = self.interact_core(preloads=preloads, trans_pause=trans_pause, pause=pause, pause_start=pause_start, pause_modal=pause_modal, **kwargs) # type: ignore
  File "D:\femc kaifa\renpy-8.3.7-sdk\renpy\display\core.py", line 2748, in interact_core
    root_widget.visit_all(lambda d : d.per_interact())
  File "D:\femc kaifa\renpy-8.3.7-sdk\renpy\display\displayable.py", line 434, in visit_all
    d.visit_all(callback, seen)
  File "D:\femc kaifa\renpy-8.3.7-sdk\renpy\display\displayable.py", line 434, in visit_all
    d.visit_all(callback, seen)
  File "D:\femc kaifa\renpy-8.3.7-sdk\renpy\display\displayable.py", line 434, in visit_all
    d.visit_all(callback, seen)
  File "D:\femc kaifa\renpy-8.3.7-sdk\renpy\display\screen.py", line 480, in visit_all
    callback(self)
  File "D:\femc kaifa\renpy-8.3.7-sdk\renpy\display\core.py", line 2748, in <lambda>
    root_widget.visit_all(lambda d : d.per_interact())
  File "D:\femc kaifa\renpy-8.3.7-sdk\renpy\display\screen.py", line 491, in per_interact
    self.update()
  File "D:\femc kaifa\renpy-8.3.7-sdk\renpy\display\screen.py", line 700, in update
    self.screen.function(**self.scope)
  File "game/screens.rpy", line 95, in execute
    screen say(who, what):
  File "game/screens.rpy", line 95, in execute
    screen say(who, what):
  File "game/screens.rpy", line 97, in execute
    window:
  File "game/screens.rpy", line 101, in execute
    if who is not None:
  File "game/screens.rpy", line 102, in execute
    window:
  File "D:\femc kaifa\renpy-8.3.7-sdk\renpy\atl.py", line 551, in take_execution_state
    block = self.compile()
  File "D:\femc kaifa\renpy-8.3.7-sdk\renpy\atl.py", line 723, in compile
    block = self.atl.compile(self.context)
  File "D:\femc kaifa\renpy-8.3.7-sdk\renpy\atl.py", line 925, in compile
    statements = [ i.compile(ctx) for i in self.statements ]
  File "D:\femc kaifa\renpy-8.3.7-sdk\renpy\atl.py", line 925, in <listcomp>
    statements = [ i.compile(ctx) for i in self.statements ]
  File "D:\femc kaifa\renpy-8.3.7-sdk\renpy\atl.py", line 1232, in compile
    child = ctx.eval(expr)
  File "D:\femc kaifa\renpy-8.3.7-sdk\renpy\atl.py", line 387, in eval
    return renpy.python.py_eval(expr, locals=self.context)
  File "D:\femc kaifa\renpy-8.3.7-sdk\renpy\python.py", line 1218, in py_eval
    return py_eval_bytecode(code, globals, locals)
  File "D:\femc kaifa\renpy-8.3.7-sdk\renpy\python.py", line 1211, in py_eval_bytecode
    return eval(bytecode, globals, locals)
  File "game/screens.rpy", line 106, in <module>
    zorder -1  # 将姓名框放到较低层级
NameError: name 'zorder' is not defined

Windows-10-10.0.22000 AMD64
Ren'Py 8.3.7.25031702
p4p 1.0
Fri Jun 27 22:25:21 2025

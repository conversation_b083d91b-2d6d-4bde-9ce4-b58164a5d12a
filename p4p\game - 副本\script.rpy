﻿# 游戏的脚本可置于此文件中。

# 声明此游戏使用的角色。颜色参数可使角色姓名着色。

define e = Character("艾琳")
define igor = Character("伊格尔")


# 游戏在此开始。

label start:

    # 显示一个背景。此处默认显示占位图，但您也可以在图片目录添加一个文件
    # （命名为 bg room.png 或 bg room.jpg）来显示。

    scene bg room

    # 显示角色立绘。此处使用了占位图，但您也可以在图片目录添加命名为
    # eileen happy.png 的文件来将其替换掉。

    show eileen happy

    # 此处显示各行对话。

    igor "总是使用同一套卡牌，"

    igor "然而每次出现的结果，却各不相同......"

    e "这是一个测试对话，用来展示新的对话框样式。"

    e "您可以看到姓名框和对话框现在使用了新的图形素材。"

    e "姓名框应该显示在对话框的上方左侧。"

    # 此处为游戏结尾。

    return

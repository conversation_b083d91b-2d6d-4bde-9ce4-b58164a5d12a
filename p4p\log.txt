2025-06-27 11:36:14 UTC
Windows-10-10.0.22000
Ren'Py 8.3.7.25031702

Early init took 0.06s
Loading error handling took 0.02s
Loading script took 0.20s
Loading save slot metadata took 0.03s
Loading persistent took 0.00s
Running init code took 0.03s
Loading analysis data took 0.01s
Analyze and compile ATL took 0.00s
Reloading save slot metadata took 0.02s
Index archives took 0.00s
Backing up script files to 'C:\\Users\\<USER>\\AppData\\Roaming/RenPy/backups\\p4p':
Dump and make backups took 0.04s
Cleaning cache took 0.00s
Making clean stores took 0.00s
Initial gc took 0.03s
DPI scale factor: 1.250000
nvdrs: Loaded, about to disable thread optimizations.
nvdrs: b"Couldn't load nvlib." (can be ignored)
Creating interface object took 0.00s
Cleaning stores took 0.00s
Init translation took 0.00s
Build styles took 0.00s
Load screen analysis took 0.00s
Analyze screens took 0.01s
Save screen analysis took 0.02s
Prepare screens took 0.05s
Save pyanalysis. took 0.00s
Save bytecode. took 0.02s
Running _start took 0.00s

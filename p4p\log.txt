2025-06-27 14:23:22 UTC
Windows-10-10.0.22000
Ren'Py 8.3.7.25031702

Early init took 0.08s
Loading error handling took 0.03s
Loading script took 0.31s
Loading save slot metadata took 0.03s
Loading persistent took 0.00s
Running init code took 0.05s
Loading analysis data took 0.01s
Analyze and compile ATL took 0.00s
Reloading save slot metadata took 0.02s
Index archives took 0.00s
Backing up script files to 'C:\\Users\\<USER>\\AppData\\Roaming/RenPy/backups\\p4p':
Dump and make backups took 0.06s
Cleaning cache took 0.00s
Making clean stores took 0.00s
Initial gc took 0.03s
DPI scale factor: 1.250000
nvdrs: Loaded, about to disable thread optimizations.
nvdrs: b"Couldn't load nvlib." (can be ignored)
Creating interface object took 0.00s
Cleaning stores took 0.00s
Init translation took 0.00s
Build styles took 0.00s
Load screen analysis took 0.00s
Analyze screens took 0.02s
Save screen analysis took 0.02s
Prepare screens took 0.09s
Save pyanalysis. took 0.02s
Save bytecode. took 0.03s
Running _start took 0.00s
Audio and video init failed. Proceeding anyway.
Traceback (most recent call last):
  File "D:\femc kaifa\renpy-8.3.7-sdk\renpy\audio\audio.py", line 1056, in init
    renpysound.init(renpy.config.sound_sample_rate, 2, bufsize, False, renpy.config.equal_mono, renpy.config.linear_fades)
  File "renpysound.pyx", line 466, in renpy.audio.renpysound.init
  File "renpysound.pyx", line 111, in renpy.audio.renpysound.check_error
Exception: WASAPI can't determine mix format: 拒绝访问。
Interface start took 0.45s

Initializing gl2 renderer:
primary display bounds: (0, 0, 1920, 1080)
swap interval: 1 frames
Windowed mode.
Vendor: "b'Intel'"
Renderer: b'Intel(R) Iris(R) Xe Graphics'
Version: b'4.6.0 - Build 30.0.100.9864'
Display Info: None
Screen sizes: virtual=(1920, 1080) physical=(1420, 799) drawable=(1420, 799)
Maximum texture size: 4096x4096

{"error": false, "size": [1920, 1080], "name": "p4p", "version": "1.0", "location": {"label": {"start": ["game/script.rpy", 10]}, "define": {"gui.system_font": ["game/tl/None/common.rpym", 1516], "config.check_conflicting_properties": ["game/gui.rpy", 15], "gui.accent_color": ["game/gui.rpy", 28], "gui.idle_color": ["game/gui.rpy", 31], "gui.idle_small_color": ["game/gui.rpy", 34], "gui.hover_color": ["game/gui.rpy", 37], "gui.selected_color": ["game/gui.rpy", 41], "gui.insensitive_color": ["game/gui.rpy", 44], "gui.muted_color": ["game/gui.rpy", 48], "gui.hover_muted_color": ["game/gui.rpy", 49], "gui.text_color": ["game/gui.rpy", 52], "gui.interface_text_color": ["game/gui.rpy", 53], "gui.text_font": ["game/gui.rpy", 59], "gui.name_text_font": ["game/gui.rpy", 62], "gui.interface_text_font": ["game/gui.rpy", 65], "gui.text_size": ["game/gui.rpy", 68], "gui.name_text_size": ["game/gui.rpy", 71], "gui.interface_text_size": ["game/gui.rpy", 74], "gui.label_text_size": ["game/gui.rpy", 77], "gui.notify_text_size": ["game/gui.rpy", 80], "gui.title_text_size": ["game/gui.rpy", 83], "gui.main_menu_background": ["game/gui.rpy", 89], "gui.game_menu_background": ["game/gui.rpy", 90], "gui.textbox_height": ["game/gui.rpy", 98], "gui.textbox_yalign": ["game/gui.rpy", 101], "gui.name_xpos": ["game/gui.rpy", 106], "gui.name_ypos": ["game/gui.rpy", 107], "gui.name_xalign": ["game/gui.rpy", 110], "gui.namebox_width": ["game/gui.rpy", 113], "gui.namebox_height": ["game/gui.rpy", 114], "gui.namebox_borders": ["game/gui.rpy", 117], "gui.namebox_tile": ["game/gui.rpy", 120], "gui.dialogue_xpos": ["game/gui.rpy", 125], "gui.dialogue_ypos": ["game/gui.rpy", 126], "gui.dialogue_width": ["game/gui.rpy", 129], "gui.dialogue_text_xalign": ["game/gui.rpy", 132], "gui.button_width": ["game/gui.rpy", 140], "gui.button_height": ["game/gui.rpy", 141], "gui.button_borders": ["game/gui.rpy", 144], "gui.button_tile": ["game/gui.rpy", 147], "gui.button_text_font": ["game/gui.rpy", 150], "gui.button_text_size": ["game/gui.rpy", 153], "gui.button_text_idle_color": ["game/gui.rpy", 156], "gui.button_text_hover_color": ["game/gui.rpy", 157], "gui.button_text_selected_color": ["game/gui.rpy", 158], "gui.button_text_insensitive_color": ["game/gui.rpy", 159], "gui.button_text_xalign": ["game/gui.rpy", 163], "gui.radio_button_borders": ["game/gui.rpy", 171], "gui.check_button_borders": ["game/gui.rpy", 173], "gui.confirm_button_text_xalign": ["game/gui.rpy", 175], "gui.page_button_borders": ["game/gui.rpy", 177], "gui.quick_button_borders": ["game/gui.rpy", 179], "gui.quick_button_text_size": ["game/gui.rpy", 180], "gui.quick_button_text_idle_color": ["game/gui.rpy", 181], "gui.quick_button_text_selected_color": ["game/gui.rpy", 182], "gui.choice_button_width": ["game/gui.rpy", 194], "gui.choice_button_height": ["game/gui.rpy", 195], "gui.choice_button_tile": ["game/gui.rpy", 196], "gui.choice_button_borders": ["game/gui.rpy", 197], "gui.choice_button_text_font": ["game/gui.rpy", 198], "gui.choice_button_text_size": ["game/gui.rpy", 199], "gui.choice_button_text_xalign": ["game/gui.rpy", 200], "gui.choice_button_text_idle_color": ["game/gui.rpy", 201], "gui.choice_button_text_hover_color": ["game/gui.rpy", 202], "gui.choice_button_text_insensitive_color": ["game/gui.rpy", 203], "gui.slot_button_width": ["game/gui.rpy", 212], "gui.slot_button_height": ["game/gui.rpy", 213], "gui.slot_button_borders": ["game/gui.rpy", 214], "gui.slot_button_text_size": ["game/gui.rpy", 215], "gui.slot_button_text_xalign": ["game/gui.rpy", 216], "gui.slot_button_text_idle_color": ["game/gui.rpy", 217], "gui.slot_button_text_selected_idle_color": ["game/gui.rpy", 218], "gui.slot_button_text_selected_hover_color": ["game/gui.rpy", 219], "config.thumbnail_width": ["game/gui.rpy", 222], "config.thumbnail_height": ["game/gui.rpy", 223], "gui.file_slot_cols": ["game/gui.rpy", 226], "gui.file_slot_rows": ["game/gui.rpy", 227], "gui.navigation_xpos": ["game/gui.rpy", 235], "gui.skip_ypos": ["game/gui.rpy", 238], "gui.notify_ypos": ["game/gui.rpy", 241], "gui.choice_spacing": ["game/gui.rpy", 244], "gui.navigation_spacing": ["game/gui.rpy", 247], "gui.pref_spacing": ["game/gui.rpy", 250], "gui.pref_button_spacing": ["game/gui.rpy", 253], "gui.page_spacing": ["game/gui.rpy", 256], "gui.slot_spacing": ["game/gui.rpy", 259], "gui.main_menu_text_xalign": ["game/gui.rpy", 262], "gui.frame_borders": ["game/gui.rpy", 270], "gui.confirm_frame_borders": ["game/gui.rpy", 273], "gui.skip_frame_borders": ["game/gui.rpy", 276], "gui.notify_frame_borders": ["game/gui.rpy", 279], "gui.frame_tile": ["game/gui.rpy", 282], "gui.bar_size": ["game/gui.rpy", 292], "gui.scrollbar_size": ["game/gui.rpy", 293], "gui.slider_size": ["game/gui.rpy", 294], "gui.bar_tile": ["game/gui.rpy", 297], "gui.scrollbar_tile": ["game/gui.rpy", 298], "gui.slider_tile": ["game/gui.rpy", 299], "gui.bar_borders": ["game/gui.rpy", 302], "gui.scrollbar_borders": ["game/gui.rpy", 303], "gui.slider_borders": ["game/gui.rpy", 304], "gui.vbar_borders": ["game/gui.rpy", 307], "gui.vscrollbar_borders": ["game/gui.rpy", 308], "gui.vslider_borders": ["game/gui.rpy", 309], "gui.unscrollable": ["game/gui.rpy", 313], "config.history_length": ["game/gui.rpy", 321], "gui.history_height": ["game/gui.rpy", 324], "gui.history_spacing": ["game/gui.rpy", 327], "gui.history_name_xpos": ["game/gui.rpy", 330], "gui.history_name_ypos": ["game/gui.rpy", 331], "gui.history_name_width": ["game/gui.rpy", 332], "gui.history_name_xalign": ["game/gui.rpy", 333], "gui.history_text_xpos": ["game/gui.rpy", 336], "gui.history_text_ypos": ["game/gui.rpy", 337], "gui.history_text_width": ["game/gui.rpy", 338], "gui.history_text_xalign": ["game/gui.rpy", 339], "gui.nvl_borders": ["game/gui.rpy", 347], "gui.nvl_list_length": ["game/gui.rpy", 351], "gui.nvl_height": ["game/gui.rpy", 354], "gui.nvl_spacing": ["game/gui.rpy", 358], "gui.nvl_name_xpos": ["game/gui.rpy", 361], "gui.nvl_name_ypos": ["game/gui.rpy", 362], "gui.nvl_name_width": ["game/gui.rpy", 363], "gui.nvl_name_xalign": ["game/gui.rpy", 364], "gui.nvl_text_xpos": ["game/gui.rpy", 367], "gui.nvl_text_ypos": ["game/gui.rpy", 368], "gui.nvl_text_width": ["game/gui.rpy", 369], "gui.nvl_text_xalign": ["game/gui.rpy", 370], "gui.nvl_thought_xpos": ["game/gui.rpy", 373], "gui.nvl_thought_ypos": ["game/gui.rpy", 374], "gui.nvl_thought_width": ["game/gui.rpy", 375], "gui.nvl_thought_xalign": ["game/gui.rpy", 376], "gui.nvl_button_xpos": ["game/gui.rpy", 379], "gui.nvl_button_xalign": ["game/gui.rpy", 380], "gui.language": ["game/gui.rpy", 388], "quick_menu": ["game/screens.rpy", 258], "gui.history_allow_tags": ["game/screens.rpy", 912], "config.nvl_list_length": ["game/screens.rpy", 1333], "bubble.frame": ["game/screens.rpy", 1441], "bubble.thoughtframe": ["game/screens.rpy", 1442], "bubble.properties": ["game/screens.rpy", 1444], "bubble.expand_area": ["game/screens.rpy", 1470], "config.name": ["game/options.rpy", 14], "gui.show_name": ["game/options.rpy", 19], "config.version": ["game/options.rpy", 24], "gui.about": ["game/options.rpy", 30], "build.name": ["game/options.rpy", 37], "config.has_sound": ["game/options.rpy", 45], "config.has_music": ["game/options.rpy", 46], "config.has_voice": ["game/options.rpy", 47], "config.enter_transition": ["game/options.rpy", 70], "config.exit_transition": ["game/options.rpy", 71], "config.intra_transition": ["game/options.rpy", 76], "config.after_load_transition": ["game/options.rpy", 81], "config.end_game_transition": ["game/options.rpy", 86], "config.window": ["game/options.rpy", 102], "config.window_show_transition": ["game/options.rpy", 107], "config.window_hide_transition": ["game/options.rpy", 108], "config.save_directory": ["game/options.rpy", 135], "config.window_icon": ["game/options.rpy", 142], "e": ["game/script.rpy", 5]}, "screen": {"say": ["game/screens.rpy", 95], "input": ["game/screens.rpy", 169], "choice": ["game/screens.rpy", 201], "quick_menu": ["game/screens.rpy", 1490], "navigation": ["game/screens.rpy", 278], "main_menu": ["game/screens.rpy", 340], "game_menu": ["game/screens.rpy", 403], "about": ["game/screens.rpy", 533], "save": ["game/screens.rpy", 571], "load": ["game/screens.rpy", 578], "file_slots": ["game/screens.rpy", 585], "preferences": ["game/screens.rpy", 713], "history": ["game/screens.rpy", 872], "help": ["game/screens.rpy", 959], "keyboard_help": ["game/screens.rpy", 988], "mouse_help": ["game/screens.rpy", 1039], "gamepad_help": ["game/screens.rpy", 1062], "confirm": ["game/screens.rpy", 1126], "skip_indicator": ["game/screens.rpy", 1188], "notify": ["game/screens.rpy", 1242], "nvl": ["game/screens.rpy", 1281], "nvl_dialogue": ["game/screens.rpy", 1313], "bubble": ["game/screens.rpy", 1399]}, "transform": {"delayed_blink": ["game/screens.rpy", 1206], "notify_appear": ["game/screens.rpy", 1253]}, "callable": {"touch": ["game/gui.rpy", 399], "small": ["game/gui.rpy", 405]}}, "build": {"directory_name": "p4p-1.0", "executable_name": "p4p", "include_update": false, "packages": [{"name": "gameonly", "formats": ["null"], "file_lists": ["all"], "description": "Game-Only Update for Mobile", "update": true, "dlc": false, "hidden": true}, {"name": "pc", "formats": ["zip"], "file_lists": ["windows", "linux", "renpy", "all"], "description": "PC: Windows and Linux", "update": true, "dlc": false, "hidden": false}, {"name": "linux", "formats": ["tar.bz2"], "file_lists": ["linux", "linux_arm", "renpy", "all"], "description": "Linux", "update": true, "dlc": false, "hidden": false}, {"name": "mac", "formats": ["app-zip", "app-dmg"], "file_lists": ["mac", "renpy", "all"], "description": "Macintosh", "update": true, "dlc": false, "hidden": false}, {"name": "win", "formats": ["zip"], "file_lists": ["windows", "renpy", "all"], "description": "Windows", "update": true, "dlc": false, "hidden": false}, {"name": "market", "formats": ["bare-zip"], "file_lists": ["windows", "linux", "mac", "renpy", "all"], "description": "Windows, Mac, Linux for Markets", "update": true, "dlc": false, "hidden": false}, {"name": "steam", "formats": ["zip"], "file_lists": ["windows", "linux", "mac", "renpy", "all"], "description": "steam", "update": true, "dlc": false, "hidden": true}, {"name": "android", "formats": ["directory"], "file_lists": ["android", "all"], "description": "android", "update": false, "dlc": true, "hidden": true}, {"name": "ios", "formats": ["directory"], "file_lists": ["ios", "all"], "description": "ios", "update": false, "dlc": true, "hidden": true}, {"name": "web", "formats": ["zip"], "file_lists": ["web", "renpy", "all"], "description": "web", "update": false, "dlc": true, "hidden": true}], "archives": [["archive", ["all"]]], "documentation_patterns": ["*.html", "*.txt"], "base_patterns": [["*.py", null], ["*.sh", null], ["*.app/", null], ["*.dll", null], ["*.manifest", null], ["*.keystore", null], ["**.rpe.py", null], ["update.pem", null], ["lib/", null], ["renpy/", null], ["update/", null], ["common/", null], ["update/", null], ["old-game/", null], ["base/", null], ["icon.ico", null], ["icon.icns", null], ["project.json", null], ["log.txt", null], ["errors.txt", null], ["traceback.txt", null], ["image_cache.txt", null], ["text_overflow.txt", null], ["dialogue.txt", null], ["dialogue.tab", null], ["profile_screen.txt", null], ["files.txt", null], ["memory.txt", null], ["tmp/", null], ["game/saves/", null], ["game/bytecode.rpyb", null], ["archived/", null], ["launcherinfo.py", null], ["android.txt", null], ["game/presplash*.*", ["all"]], ["android.json", ["android"]], [".android.json", ["android"]], ["android-*.png", ["android"]], ["android-*.jpg", ["android"]], ["ouya_icon.png", null], ["ios-presplash.*", ["ios"]], ["ios-launchimage.png", null], ["ios-icon.png", null], ["web-presplash.png", ["web"]], ["web-presplash.jpg", ["web"]], ["web-presplash.webp", ["web"]], ["web-icon.png", ["web"]], ["progressive_download.txt", ["web"]], ["steam_appid.txt", null], ["game/cache/bytecode-39.rpyb", ["all"]], ["game/cache/bytecode-311.rpyb", ["web"]], ["game/cache/bytecode-*.rpyb", null], ["game/cache/build_info.json", null], ["game/cache/build_time.txt", null], ["**~", null], ["**.bak", null], ["**/.**", null], ["**/#**", null], ["**/thumbs.db", null], [".*", null], ["**", ["all"]]], "renpy_patterns": [["renpy/common/_compat/**", null], ["renpy/common/_roundrect/**", null], ["renpy/common/_outline/**", null], ["renpy/common/_theme**", null], ["renpy/**__pycache__/**.cpython-39.pyc", ["all"]], ["renpy/**__pycache__", ["all"]], ["**~", null], ["**/#*", null], ["**/.*", null], ["**.old", null], ["**.new", null], ["**.rpa", null], ["**.rpe", null], ["**.rpe.py", null], ["**/steam_appid.txt", null], ["renpy.py", ["all"]], ["renpy/", ["all"]], ["renpy/**.py", ["renpy"]], ["renpy/**.pxd", null], ["renpy/**.pxi", null], ["renpy/**.pyx", null], ["renpy/**.pyc", null], ["renpy/**.pyo", null], ["renpy/common/", ["all"]], ["renpy/common/_compat/**", ["renpy"]], ["renpy/common/**.rpy", ["renpy"]], ["renpy/common/**.rpym", ["renpy"]], ["renpy/common/_compat/**", ["renpy"]], ["renpy/common/**", ["all"]], ["renpy/**", ["all"]], ["lib/*/renpy", null], ["lib/*/renpy.exe", null], ["lib/*/pythonw.exe", null], ["lib/py2-*/", null], ["lib/py*-windows-i686/**", ["windows_i686"]], ["lib/py*-windows-x86_64/**", ["windows"]], ["lib/py*-linux-i686/**", ["linux_i686"]], ["lib/py*-linux-aarch64/**", ["linux_arm"]], ["lib/py*-linux-armv7l/**", ["linux_arm"]], ["lib/py*-linux-*/**", ["linux"]], ["lib/py*-mac-*/**", ["mac"]], ["lib/python2.*/**", null], ["lib/**", ["windows", "linux", "mac", "android", "ios"]], ["renpy.sh", ["linux", "mac"]]], "xbit_patterns": ["**.sh", "lib/py*-linux-*/*", "lib/py*-mac-*/*", "**.app/Contents/MacOS/*"], "version": "1.0", "display_name": "p4p", "exclude_empty_directories": true, "allow_integrated_gpu": true, "renpy": false, "script_version": true, "destination": "p4p-1.0-dists", "itch_channels": {"*-all.zip": "win-osx-linux", "*-market.zip": "win-osx-linux", "*-pc.zip": "win-linux", "*-win.zip": "win", "*-mac.zip": "osx", "*-linux.tar.bz2": "linux", "*-release.apk": "android"}, "mac_info_plist": {}, "merge": [["linux_i686", "linux"], ["windows_i686", "windows"]], "include_i686": true, "change_icon_i686": true, "android_permissions": [], "_sdk_fonts": false, "update_formats": ["rpu"], "info": {"info": {}, "time": 1751016814.2024758, "name": "p4p", "version": "1.0"}}}